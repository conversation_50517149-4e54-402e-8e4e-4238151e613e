import request from '@/utils/request'

export function fetchList(params) {
  return request({
    url: '/admin/pixelPicCategory',
    method: 'get',
    params: params
  })
}

export function createPixelPicCategory(data) {
  return request({
    url: '/admin/pixelPicCategory',
    method: 'post',
    data: data
  })
}

export function deletePixelPicCategory(id) {
  return request({
    url: '/admin/pixelPicCategory/' + id,
    method: 'delete'
  })
}

export function updatePixelPicCategory(id, data) {
  return request({
    url: '/admin/pixelPicCategory/' + id,
    method: 'patch',
    data: data
  })
}

export function getPixelPicCategory(id) {
  return request({
    url: '/admin/pixelPicCategory/' + id,
    method: 'get',
  })
}


