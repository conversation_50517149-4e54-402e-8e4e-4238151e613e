import request from '@/utils/request'

export function login(username, password) {
  return request({
    url: '/admin/ums/login',
    method: 'post',
    data: {
      username,
      password
    }
  })
}

export function getInfo() {
  return request({
    url: '/admin/ums/info',
    method: 'get',
  })
}

export function logout() {
  return request({
    url: '/admin/ums/logout',
    method: 'post'
  })
}

export function fetchList(params) {
  return request({
    url: '/admin/ums/list',
    method: 'get',
    params: params
  })
}

export function createAdmin(data) {
  return request({
    url: '/admin/ums/register',
    method: 'post',
    data: data
  })
}

export function updateAdmin(id, data) {
  return request({
    url: '/admin/ums/update/' + id,
    method: 'post',
    data: data
  })
}

export function updateStatus(id, params) {
  return request({
    url: '/admin/ums/updateStatus/' + id,
    method: 'post',
    params: params
  })
}

export function deleteAdmin(id) {
  return request({
    url: '/admin/ums/delete/' + id,
    method: 'post'
  })
}

export function getRoleByAdmin(id) {
  return request({
    url: '/admin/ums/role/' + id,
    method: 'get'
  })
}

export function allocRole(data) {
  return request({
    url: '/admin/ums/role/update',
    method: 'post',
    data: data
  })
}
