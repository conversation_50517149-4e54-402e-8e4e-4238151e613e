import request from '@/utils/request'

export function fetchList(params) {
  return request({
    url: '/admin/pixelPic',
    method: 'get',
    params: params
  })
}

export function fetchSizeList() {
  return request({
    url: '/admin/pixelPic/size',
    method: 'get',
  })
}

export function createPixelPic(data) {
  return request({
    url: '/admin/pixelPic',
    method: 'post',
    data: data
  })
}

export function updateShowStatus(data) {
  return request({
    url: '/admin/pixelPic/showStatus',
    method: 'patch',
    data: data
  })
}

export function updateFactoryStatus(data) {
  return request({
    url: '/admin/pixelPic/factoryStatus',
    method: 'post',
    data: data
  })
}

export function deletePixelPic(id) {
  return request({
    url: '/admin/pixelPic/' + id,
    method: 'delete',
  })
}

export function getPixelPic(id) {
  return request({
    url: '/admin/pixelPic/' + id,
    method: 'get',
  })
}

export function updatePixelPic(id, data) {
  return request({
    url: '/admin/pixelPic/' + id,
    method: 'patch',
    data: data
  })
}

