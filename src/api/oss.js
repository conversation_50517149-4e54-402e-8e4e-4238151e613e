import request from '@/utils/request'

/**
 * 图片的上传策略
 * @returns {*}
 */
export function policy() {
  return request({
    url: '/admin/aliyun/oss/policy/image',
    method: 'get',
  })
}

/**
 * 固件的上传策略
 * @returns {*}
 */
export function binPolicy() {
  return request({
    url: '/admin/aliyun/oss/policy/bin',
    method: 'get',
  })
}

export function iconPolicy() {
  return request({
    url: '/admin/aliyun/oss/policy/icon',
    method: 'get',
  })
}

export function apkPolicy() {
  return request({
    url: '/admin/aliyun/oss/policy/apk',
    method: 'get',
  })
}
