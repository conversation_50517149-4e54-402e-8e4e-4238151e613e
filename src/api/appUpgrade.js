import request from '@/utils/request'

export function fetchList(params) {
  return request({
    url: '/admin/appUpgrade', method: 'get', params: params
  })
}

export function createAppUpgrade(data) {
  return request({
    url: '/admin/appUpgrade', method: 'post', data: data
  })
}

export function updateShowStatus(data) {
  return request({
    url: '/admin/appUpgrade/showStatus', method: 'patch', data: data
  })
}

export function deleteAppUpgrade(id) {
  return request({
    url: '/admin/appUpgrade/' + id, method: 'delete',
  })
}

export function getAppUpgrade(id) {
  return request({
    url: '/admin/appUpgrade/' + id, method: 'get',
  })
}

export function updateAppUpgrade(id, data) {
  return request({
    url: '/admin/appUpgrade/' + id, method: 'patch', data: data
  })
}

export function getLastAppUpgrade() {
  return request({
    url: '/admin/appUpgrade/last', method: 'get'
  })
}

