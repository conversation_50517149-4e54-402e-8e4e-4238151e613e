import request from '@/utils/request'

export function fetchList(params) {
  return request({
    url: '/admin/deviceUpgrade',
    method: 'get',
    params: params
  })
}

export function createDeviceUpgrade(data) {
  return request({
    url: '/admin/deviceUpgrade',
    method: 'post',
    data: data
  })
}

export function updateShowStatus(data) {
  return request({
    url: '/admin/deviceUpgrade/showStatus',
    method: 'patch',
    data: data
  })
}

export function deleteDeviceUpgrade(id) {
  return request({
    url: '/admin/deviceUpgrade/' + id,
    method: 'delete',
  })
}

export function getDeviceUpgrade(id) {
  return request({
    url: '/admin/deviceUpgrade/' + id,
    method: 'get',
  })
}

export function updateDeviceUpgrade(id, data) {
  return request({
    url: '/admin/deviceUpgrade/' + id,
    method: 'patch',
    data: data
  })
}

export function getLastDeviceUpgradeByDeviceSize(deviceType) {
  return request({
    url: '/admin/deviceUpgrade/last/' + deviceType,
    method: 'get'
  })
}

