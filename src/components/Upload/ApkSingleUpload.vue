<template>
  <div>
    <el-upload
      :action="useOss?ossUploadUrl:minioUploadUrl"
      :data="useOss?dataObj:null"
      list-type="file"
      :multiple="false"
      :show-file-list="showFileList"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :auto-upload="true"
      :on-remove="handleRemove"
      :on-success="handleUploadSuccess"
      :on-preview="handlePreview">
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">只能上传APK文件，且不超过300MB</div>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible">
      <img height="100%" :src="fileList[0].url" alt="">
    </el-dialog>
  </div>
</template>
<script>
import {apkPolicy} from '@/api/oss'

const AppInfoParser = require('app-info-parser')

export default {
  name: 'ApkSingleUpload',
  props: {
    value: String,
    lastVersionInt: String,
    lastVersion: String,
    acceptType: Array,
    acceptSize: String
  },
  created() {
    //console.log(`acceptSize ${this.$props.acceptSize}`)
  },
  computed: {
    imageUrl() {
      return this.value;
    },
    imageName() {
      if (this.value != null && this.value !== '') {
        return this.value.substr(this.value.lastIndexOf("/") + 1);
      } else {
        return null;
      }
    },
    fileList() {
      return [{
        name: this.imageName,
        url: this.imageUrl
      }]
    },
    showFileList: {
      get: function () {
        return this.value !== null && this.value !== '' && this.value !== undefined;
      },
      set: function (newValue) {
      }
    }
  },
  data() {
    return {
      dataObj: {
        policy: '',
        signature: '',
        key: '',
        ossaccessKeyId: '',
        dir: '',
        host: '',
        // callback:'',
      },
      dialogVisible: false,
      useOss: true, //使用oss->true;使用MinIO->false
      ossUploadUrl: 'https://download.chin.city',// 原来https://lighting-oss.oss-cn-shenzhen.aliyuncs.com
      minioUploadUrl: 'http://localhost:8080/minio/upload',
    };
  },
  methods: {
    emitInput(val) {
      this.$emit('input', val)
    },
    emitApkInfo(val) {
      this.$emit('info', val)
    },
    handleRemove(file, fileList) {
      this.emitInput('');
    },
    handlePreview(file) {
      this.dialogVisible = true;
      //this.emitInput(file[0].url)
    },
    async beforeUpload(file) {
      let fileMBSize = file.size / 1024 / 1024;
      // console.log("fileSize " + fileSize)

      let fileName = file.name;
      console.log("fileName " + fileName)

      let fileType = fileName.substring(fileName.lastIndexOf('.'));
      console.log("fileType " + fileType)
      if (fileType !== '.apk') {
        this.$message({
          message: '只能上传APK文件',
          type: 'error',
          duration: 1000
        });
        return;
      }
      // file.name = '1111111111111' + fileType;


      if (fileMBSize > 300) {
        this.$message({
          message: '只能上传300MB以内的APK文件',
          type: 'error',
          duration: 1000
        });
        return;
      }

      let _self = this;
      if (!this.useOss) {
        //不使用oss不需要获取策略
        return true;
      }
      // let currentDate = new Date();
      // let randomFileName = currentDate.toLocaleString() + fileType;
      //
      console.log("goto check readApkInfo ")

      let checkApkResult = await this.readApkInfo(file);

      console.log("apk fileName result " + JSON.stringify(checkApkResult))
      if (checkApkResult.package !== 'com.thomson.lighting_app') {
        this.$message({
          message: '只能上传Popusign APK',
          type: 'error',
          duration: 1000
        });
        return;
      }

      if (this.lastVersion != null && checkApkResult.versionName < this.lastVersion) {
        this.$message({
          message: '只能上传更高版本的APK',
          type: 'error',
          duration: 1000
        });
        return;
      }

      if (this.lastVersionInt != null && checkApkResult.versionCode <= this.lastVersionInt) {
        this.$message({
          message: '只能上传更高版本的APK',
          type: 'error',
          duration: 1000
        });
        return;
      }
      if (checkApkResult != null) {
        this.emitApkInfo(checkApkResult);

        return new Promise((resolve, reject) => {
          apkPolicy().then(response => {
            _self.dataObj.policy = response.data.policy;
            _self.dataObj.signature = response.data.signature;
            _self.dataObj.ossaccessKeyId = response.data.accessKeyId;
            _self.dataObj.key = response.data.dir + '/${filename}';
            _self.dataObj.dir = response.data.dir;
            _self.dataObj.host = response.data.host;
            // _self.dataObj.callback = response.data.callback;
            resolve(true)
          }).catch(err => {
            console.log(err)
            reject(false)
          })
        })
      }
    },
    handleUploadSuccess(res, file) {
      this.showFileList = true;
      this.fileList.pop();
      let url = this.dataObj.host + '/' + this.dataObj.dir + '/' + file.name;
      if (!this.useOss) {
        //不使用oss直接获取图片路径
        url = res.data.url;
      }
      this.fileList.push({name: file.name, url: url});
      this.emitInput(this.fileList[0].url);
    },
    readApkInfo(file) {
      return new Promise((resolve, reject) => {
        // let acceptSize = this.$props.acceptSize;
        // let array = acceptSize.split("x");
        // let limitWidth = parseFloat(array[0])
        // let limitHeight = parseFloat(array[1])
        const parser = new AppInfoParser(file)
        parser.parse().then(result => {
          console.log('app info ----> ', result)
          /**
           *
           * @type {{package: any, versionCode: any, versionName: any}}
           */
            // console.log('icon base64 ----> ', result.icon)
          let appInfo = {
              package: result.package,
              versionCode: result.versionCode,
              versionName: result.versionName,
            }
          resolve(appInfo);
        }).catch(err => {
          console.log('err ----> ', err)
          reject(null);
        })
      })
    }
  }
}
</script>
<style>

</style>


