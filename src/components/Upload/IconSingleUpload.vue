<template>
  <div>
    <el-upload
      :action="useOss?ossUploadUrl:minioUploadUrl"
      :data="useOss?dataObj:null"
      list-type="picture"
      :multiple="false"
      :show-file-list="showFileList"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :auto-upload="true"
      :on-remove="handleRemove"
      :on-success="handleUploadSuccess"
      :on-preview="handlePreview">
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">只能上传PNG/SVG文件，且不超过100KB</div>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible">
      <img height="100%" :src="fileList[0].url" alt="">
    </el-dialog>
  </div>
</template>
<script>
import {iconPolicy} from '@/api/oss'

export default {
  name: 'IconSingleUpload',
  props: {
    value: String,
    acceptType: Array,
    acceptSize: String
  },
  created() {
    //console.log(`acceptSize ${this.$props.acceptSize}`)
  },
  computed: {
    imageUrl() {
      return this.value;
    },
    imageName() {
      if (this.value != null && this.value !== '') {
        return this.value.substr(this.value.lastIndexOf("/") + 1);
      } else {
        return null;
      }
    },
    fileList() {
      return [{
        name: this.imageName,
        url: this.imageUrl
      }]
    },
    showFileList: {
      get: function () {
        return this.value !== null && this.value !== '' && this.value !== undefined;
      },
      set: function (newValue) {
      }
    }
  },
  data() {
    return {
      dataObj: {
        policy: '',
        signature: '',
        key: '',
        ossaccessKeyId: '',
        dir: '',
        host: '',
        // callback:'',
      },
      dialogVisible: false,
      useOss: true, //使用oss->true;使用MinIO->false
      ossUploadUrl: 'https://download.chin.city',//https://lighting-oss.oss-cn-shenzhen.aliyuncs.com
      minioUploadUrl: 'http://localhost:8080/minio/upload',
    };
  },
  methods: {
    emitInput(val) {
      this.$emit('input', val)
    },
    handleRemove(file, fileList) {
      this.emitInput('');
    },
    handlePreview(file) {
      this.dialogVisible = true;
      //this.emitInput(file[0].url)
    },
    async beforeUpload(file) {
      let fileMBSize = file.size / 1024;// 1024;
      // console.log("fileSize " + fileSize)

      let fileName = file.name;
      console.log("fileName " + fileName)

      let fileType = fileName.substring(fileName.lastIndexOf('.'));
      console.log("fileType " + fileType)
      if (fileType !== '.png' && fileType !== '.svg') {
        this.$message({
          message: '只能上传PNG/SVG图片',
          type: 'error',
          duration: 1000
        });
        return;
      }
      // file.name = '1111111111111' + fileType;


      if (fileMBSize > 100) {
        this.$message({
          message: '只能上传100KB以内的PNG/SVG图片',
          type: 'error',
          duration: 1000
        });
        return;
      }

      let _self = this;
      if (!this.useOss) {
        //不使用oss不需要获取策略
        return true;
      }
      // let currentDate = new Date();
      // let randomFileName = currentDate.toLocaleString() + fileType;
      //
      // console.log("fileName 222 " + currentDate)

      let result = await this.compareImageWidthAndHeight(file);
      if (result === true) {
        return new Promise((resolve, reject) => {
          iconPolicy().then(response => {
            _self.dataObj.policy = response.data.policy;
            _self.dataObj.signature = response.data.signature;
            _self.dataObj.ossaccessKeyId = response.data.accessKeyId;
            _self.dataObj.key = response.data.dir + '/${filename}';
            _self.dataObj.dir = response.data.dir;
            _self.dataObj.host = response.data.host;
            // _self.dataObj.callback = response.data.callback;
            resolve(true)
          }).catch(err => {
            console.log(err)
            reject(false)
          })
        })
      }
    },
    handleUploadSuccess(res, file) {
      this.showFileList = true;
      this.fileList.pop();
      let url = this.dataObj.host + '/' + this.dataObj.dir + '/' + file.name;
      if (!this.useOss) {
        //不使用oss直接获取图片路径
        url = res.data.url;
      }
      this.fileList.push({name: file.name, url: url});
      this.emitInput(this.fileList[0].url);
    },
    compareImageWidthAndHeight(file) {
      return new Promise((resolve, reject) => {
        // let acceptSize = this.$props.acceptSize;
        // let array = acceptSize.split("x");
        // let limitWidth = parseFloat(array[0])
        // let limitHeight = parseFloat(array[1])
        const reader = new FileReader();
        // 文件读取成功完成后的处理
        let that = this;
        reader.onload = function (event) {
          // 创建Image对象
          const img = new Image();
          img.onload = function () {
            // 获取图片尺寸
            const width = img.width;
            const height = img.height;
            // 打印尺寸或做其他处理
            console.log(`图片宽度: ${width}, 高度: ${height}`);
            if (width !== height || width <= 10 || width >= 60) {
              that.$message({
                message: `只能上传正方形10~60高宽的PNG/SVG图片`,
                type: 'error',
                duration: 1000
              });
              reject(false)
            } else {
              resolve(true)
            }
          };

          // 设置Image对象的源为FileReader读取的结果
          img.src = event.target.result;
        };

        // 以DataURL的形式读取文件内容
        reader.readAsDataURL(file);
      })
    }
  }
}
</script>
<style>

</style>


