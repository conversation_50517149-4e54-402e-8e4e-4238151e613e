<template>
  <el-card class="form-container" shadow="never">
    <el-form :model="pixelPic" :rules="rules" ref="brandFrom" label-width="150px">
      <el-form-item label="图片名称：" prop="name">
        <el-input v-model="pixelPic.name" style="width: 200px" maxlength="20" clearable></el-input>
      </el-form-item>
      <el-form-item label="图片类型：">
        <!--        <el-input v-model="pixelPic.imgType"></el-input>-->
        <el-select v-model="pixelPic.imgType" placeholder="请选择">
          <el-option
            v-for="item in pixelPicCategoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="图片尺寸：">
        <!--        <el-input v-model="pixelPic.imgType"></el-input>-->
        <el-select v-model="pixelPic.imgSize" placeholder="请选择">
          <el-option
            v-for="item in pixelPicSizes"
            :key="item.text"
            :label="item.text"
            :value="item.text">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传图片：" prop="logo">
        <PicSingleUpload v-model="pixelPic.imgUrl" :acceptType="['gif']" :acceptSize="pixelPic.imgSize"
                         @input="onFileInput"></PicSingleUpload>
      </el-form-item>
      <!--      <el-form-item label="品牌专区大图：">-->
      <!--        <single-upload v-model="brand.bigPic"></single-upload>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="品牌故事：">-->
      <!--        <el-input-->
      <!--          placeholder="请输入内容"-->
      <!--          type="textarea"-->
      <!--          v-model="brand.brandStory"-->
      <!--          :autosize="true"></el-input>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="排序：" prop="sort">-->
      <!--        <el-input v-model.number="brand.sort"></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="是否显示：">
        <el-radio-group v-model="pixelPic.showStatus">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <!--      <el-form-item label="品牌制造商：">-->
      <!--        <el-radio-group v-model="brand.factoryStatus">-->
      <!--          <el-radio :label="1">是</el-radio>-->
      <!--          <el-radio :label="0">否</el-radio>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" @click="onSubmit('brandFrom')">提交</el-button>
        <el-button v-if="!isEdit" @click="resetForm('brandFrom')">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>
<script>
import {createPixelPic, getPixelPic, updatePixelPic, fetchSizeList} from '@/api/pixelPic'
import {
  fetchList,
} from '@/api/pixelPicCategory'
import PicSingleUpload from '@/components/Upload/PicSingleUpload'

const defaultPixelPic = {
  imgUrl: '',
  name: '',
  imgType: '60x60',
  imgSize: '32x16',
  keyword: null,
  showStatus: 1,
};
export default {
  name: 'PixelPicDetail',
  components: {PicSingleUpload},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pixelPic: Object.assign({}, defaultPixelPic),
      pixelPicCategoryList: [],//类型
      pixelPicSizes: [],//尺寸大小
      rules: {
        name: [
          {required: true, message: '请输入图片名称', trigger: 'blur'},
          {min: 2, max: 140, message: '长度在 2 到 140 个字符', trigger: 'blur'}
        ],
        imgUrl: [
          {required: true, message: '请上传图片', trigger: 'blur'}
        ],
        imgType: [
          {required: true, message: '请输入图片类型', trigger: 'blur'}
        ],
      }
    }
  },
  created() {
    if (this.isEdit) {
      getPixelPic(this.$route.query.id).then(response => {
        this.pixelPic = response.data;
      });
    } else {
      this.pixelPic = Object.assign({}, defaultPixelPic);
    }
    this.loadCategories();
    this.loadPixelPicSize();
  },
  methods: {
    loadCategories() {
      fetchList({
        pageNum: 1,
        pageSize: 50
      }).then(response => {
        this.pixelPicCategoryList = response.data.list;
        if (this.pixelPicCategoryList.length > 0) {
          if (this.isEdit) {
            // this.pixelPic.imgType=
          } else {
            this.pixelPic.imgType = this.pixelPicCategoryList[0].id;
          }
        }
      });
    },
    loadPixelPicSize() {
      fetchSizeList().then(response => {
        this.pixelPicSizes = response.data;
        if (this.pixelPicSizes.length > 0) {
          if (this.isEdit) {
            // this.pixelPic.imgType=
          } else {
            this.pixelPic.imgSize = this.pixelPicSizes[0].text;
          }

        }
      });
    },
    onFileInput(e) {
      console.log("onFileInput " + e)
      // let file = new File(null, e);
      // if (!file) {
      //   return;
      // }
      // let reader = new FileReader();
      // reader.onload = function (e) {
      //   let img = new Image();
      //   img.onload = function () {
      //     console.log(`Image size: ${img.width}x${img.height}`);
      //   };
      // };
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirm('是否提交数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            if (this.isEdit) {
              updatePixelPic(this.$route.query.id, this.pixelPic).then(response => {
                this.$refs[formName].resetFields();
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  duration: 1000
                });
                this.$router.back();
              });
            } else {
              createPixelPic(this.pixelPic).then(response => {
                this.$refs[formName].resetFields();
                this.pixelPic = Object.assign({}, defaultPixelPic);
                this.$message({
                  message: '提交成功',
                  type: 'success',
                  duration: 1000
                });
                this.$router.back();
              });
            }
          });

        } else {
          this.$message({
            message: '验证失败',
            type: 'error',
            duration: 1000
          });
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.pixelPic = Object.assign({}, defaultPixelPic);
    }
  }
}
</script>
<style>
</style>


