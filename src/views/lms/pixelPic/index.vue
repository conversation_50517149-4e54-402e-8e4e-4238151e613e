<template>
  <div class="app-container">
    <el-card class="filter-container" shadow="never">
      <div>
        <i class="el-icon-search"></i>
        <span>筛选搜索</span>
        <el-button
          style="float: right"
          @click="searchBrandList()"
          type="primary"
          size="small">
          查询结果
        </el-button>
      </div>
      <div style="margin-top: 15px">
        <el-form :inline="true" :model="listQuery" size="small" label-width="140px">
          <el-form-item label="输入搜索：">
            <el-input style="width: 203px" v-model="listQuery.keyword" placeholder="图片名称/关键字"
                      clearable></el-input>
          </el-form-item>
          <el-form-item label="类型：">
            <!--            <el-input style="width: 203px" v-model="listQuery.keyword" placeholder="图片名称/关键字"></el-input>-->
            <el-select v-model="listQuery.imgType" clearable placeholder="请选择">
              <el-option
                v-for="item in categories"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="尺寸：">
            <!--            <el-input style="width: 203px" v-model="listQuery.keyword" placeholder="图片名称/关键字"></el-input>-->
            <el-select v-model="listQuery.imgSize" clearable placeholder="请选择">
              <el-option
                v-for="item in imgSizes"
                :key="item.text"
                :label="item.text"
                :value="item.text">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card class="operate-container" shadow="never">
      <i class="el-icon-tickets"></i>
      <span>数据列表</span>
      <el-button
        class="btn-add"
        @click="addBrand()"
        size="mini">
        添加
      </el-button>
    </el-card>
    <div class="table-container">
      <el-table ref="brandTable"
                :data="list"
                style="width: 100%"
                @selection-change="handleSelectionChange"
                v-loading="listLoading"
                border>
        <!--        <el-table-column type="selection" width="60" align="center"></el-table-column>-->
        <el-table-column label="编号" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.id }}</template>
        </el-table-column>
        <el-table-column label="图片名称" width="200" align="center">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column label="图片类型" width="200" align="center">
          <template slot-scope="scope">{{ scope.row.category.name }}</template>
        </el-table-column>
        <el-table-column label="图片尺寸" width="200" align="center">
          <template slot-scope="scope">{{ scope.row.imgSize }}</template>
        </el-table-column>
        <el-table-column label="图片内容" align="center">
          <!--          <template slot-scope="scope">{{ scope.row.sort }}</template>-->
          <template slot-scope="scope"><img :src="scope.row.imgUrl " height="80" alt=""/></template>
        </el-table-column>
        <!--        <el-table-column label="品牌制造商" width="100" align="center">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-switch-->
        <!--              @change="handleFactoryStatusChange(scope.$index, scope.row)"-->
        <!--              :active-value="1"-->
        <!--              :inactive-value="0"-->
        <!--              v-model="scope.row.factoryStatus">-->
        <!--            </el-switch>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="创建时间" width="200" align="center">
          <template slot-scope="scope">{{ formattedDate()(scope.row.createTime) }}</template>
        </el-table-column>
        <el-table-column label="是否显示" width="100" align="center">
          <template slot-scope="scope">
            <el-switch
              @change="handleShowStatusChange(scope.$index, scope.row)"
              :active-value="1"
              :inactive-value="0"
              v-model="scope.row.showStatus">
            </el-switch>
          </template>
        </el-table-column>
        <!--        <el-table-column label="相关" width="220" align="center">-->
        <!--          <template slot-scope="scope">-->
        <!--            <span>商品：</span>-->
        <!--            <el-button-->
        <!--              size="mini"-->
        <!--              type="text"-->
        <!--              @click="getProductList(scope.$index, scope.row)">100-->
        <!--            </el-button>-->
        <!--            <span>评价：</span>-->
        <!--            <el-button-->
        <!--              size="mini"-->
        <!--              type="text"-->
        <!--              @click="getProductCommentList(scope.$index, scope.row)">1000-->
        <!--            </el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="操作" width="200" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              @click="handleUpdate(scope.$index, scope.row)">编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!--    <div class="batch-operate-container">-->
    <!--      <el-select-->
    <!--        size="small"-->
    <!--        v-model="operateType" placeholder="批量操作">-->
    <!--        <el-option-->
    <!--          v-for="item in operates"-->
    <!--          :key="item.value"-->
    <!--          :label="item.label"-->
    <!--          :value="item.value">-->
    <!--        </el-option>-->
    <!--      </el-select>-->
    <!--      <el-button-->
    <!--        style="margin-left: 20px"-->
    <!--        class="search-button"-->
    <!--        @click="handleBatchOperate()"-->
    <!--        type="primary"-->
    <!--        size="small">-->
    <!--        确定-->
    <!--      </el-button>-->
    <!--    </div>-->
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, sizes,prev, pager, next,jumper"
        :page-size="listQuery.pageSize"
        :page-sizes="[5,10,15]"
        :current-page.sync="listQuery.pageNum"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import {
  fetchList,
  updateShowStatus,
  updateFactoryStatus,
  deletePixelPic,
  updatePixelPic,
  fetchSizeList
} from '@/api/pixelPic'
import {
  fetchList as fetchListCategory,
} from '@/api/pixelPicCategory'

import {formattedDate} from "@/utils/date";

export default {
  name: 'brandList',
  data() {
    return {
      operates: [
        {
          label: "显示品牌",
          value: "showBrand"
        },
        {
          label: "隐藏品牌",
          value: "hideBrand"
        }
      ],
      operateType: null,
      listQuery: {
        keyword: null,
        pageNum: 1,
        pageSize: 10,
        imgType: null,
        imgSize: null
      },
      list: null,
      categories: null,
      imgSizes: null,
      total: null,
      listLoading: true,
      multipleSelection: []
    }
  },
  created() {
    this.getCategories();
    this.fetchImgSizes();
    this.getList();
  },
  methods: {
    formattedDate() {
      return formattedDate
    },
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        this.listLoading = false;
        this.list = response.data.list;
        this.total = response.data.total;
        this.totalPage = response.data.totalPage;
        this.pageSize = response.data.pageSize;
      });
    },
    getCategories() {
      fetchListCategory({
        pageSize: 50,
        pageNum: 1
      }).then(response => {
        this.categories = response.data.list;
      });
    },
    fetchImgSizes() {
      fetchSizeList({
        pageSize: 50,
        pageNum: 1
      }).then(response => {
        this.imgSizes = response.data;
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleUpdate(index, row) {
      this.$router.push({path: '/lms/updatePixelPic', query: {id: row.id}})
    },
    handleDelete(index, row) {
      this.$confirm('是否要删除该图片', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePixelPic(row.id).then(response => {
          this.$message({
            message: '删除成功',
            type: 'success',
            duration: 1000
          });
          this.getList();
        });
      });
    },

    getProductList(index, row) {
      console.log(index, row);
    },
    getProductCommentList(index, row) {
      console.log(index, row);
    },
    handleFactoryStatusChange(index, row) {
      var data = new URLSearchParams();
      data.append("ids", row.id);
      data.append("factoryStatus", row.factoryStatus);
      updateFactoryStatus(data).then(response => {
        this.$message({
          message: '修改成功',
          type: 'success',
          duration: 1000
        });
      }).catch(error => {
        if (row.factoryStatus === 0) {
          row.factoryStatus = 1;
        } else {
          row.factoryStatus = 0;
        }
      });
    },
    handleShowStatusChange(index, row) {
      console.log("handleShowStatusChange " + JSON.stringify(row))

      // let data = new URLSearchParams();
      // let data = {...row};
      // data.append("ids", row.id);
      // data.append("showStatus", row.showStatus);
      updatePixelPic(row.id, row).then(response => {
        this.$message({
          message: '修改成功',
          type: 'success',
          duration: 1000
        });
      }).catch(error => {
        if (row.showStatus === 0) {
          row.showStatus = 1;
        } else {
          row.showStatus = 0;
        }
      });
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1;
      this.listQuery.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val;
      this.getList();
    },
    searchBrandList() {
      this.listQuery.pageNum = 1;
      this.getList();
    },
    handleBatchOperate() {
      console.log(this.multipleSelection);
      if (this.multipleSelection < 1) {
        this.$message({
          message: '请选择一条记录',
          type: 'warning',
          duration: 1000
        });
        return;
      }
      let showStatus = 0;
      if (this.operateType === 'showBrand') {
        showStatus = 1;
      } else if (this.operateType === 'hideBrand') {
        showStatus = 0;
      } else {
        this.$message({
          message: '请选择批量操作类型',
          type: 'warning',
          duration: 1000
        });
        return;
      }
      let ids = [];
      for (let i = 0; i < this.multipleSelection.length; i++) {
        ids.push(this.multipleSelection[i].id);
      }
      let data = new URLSearchParams();
      data.append("ids", ids);
      data.append("showStatus", showStatus);
      updateShowStatus(data).then(response => {
        this.getList();
        this.$message({
          message: '修改成功',
          type: 'success',
          duration: 1000
        });
      });
    },
    addBrand() {
      this.$router.push({path: '/lms/addPixelPic'})
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>


</style>


