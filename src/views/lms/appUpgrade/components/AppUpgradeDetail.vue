<template>
  <el-card class="form-container" shadow="never" disabled>
    <el-form :model="deviceUpgrade" :rules="rules" ref="brandFrom" label-width="150px">
      <el-form-item label="更新说明：" prop="name">
        <el-input v-model="deviceUpgrade.description" style="width: 200px" maxlength="20" clearable></el-input>
      </el-form-item>
      <!--      <el-form-item label="图片类型：">-->
      <!--        &lt;!&ndash;        <el-input v-model="pixelPic.imgType"></el-input>&ndash;&gt;-->
      <!--        <el-select v-model="pixelPic.imgType" placeholder="请选择">-->
      <!--          <el-option-->
      <!--            v-for="item in pixelPicCategoryList"-->
      <!--            :key="item.id"-->
      <!--            :label="item.name"-->
      <!--            :value="item.id">-->
      <!--          </el-option>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="设备尺寸：">-->
      <!--        &lt;!&ndash;        <el-input v-model="pixelPic.imgType"></el-input>&ndash;&gt;-->
      <!--        <el-select v-model="deviceUpgrade.deviceSize" placeholder="请选择">-->
      <!--          <el-option-->
      <!--            v-for="item in pixelPicSizes"-->
      <!--            :key="item.text"-->
      <!--            :label="item.text"-->
      <!--            :value="item.text">-->
      <!--          </el-option>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <template> {{
            this.lastAppUpgrade == null ? '没最新版本' : ('最新版本:' + this.lastAppUpgrade.versionName + ' 版本Int: ' + this.lastAppUpgrade.versionCode)
          }}
        </template>
      </el-form-item>
      <el-form-item label="新版本号：" prop="name">
        <el-input v-model="deviceUpgrade.versionName" style="width: 200px" maxlength="20" clearable disabled></el-input>
      </el-form-item>
      <el-form-item label="新版本号Int：" prop="name">
        <el-input v-model="deviceUpgrade.versionCode" style="width: 200px" maxlength="20" clearable disabled></el-input>
      </el-form-item>
      <el-form-item label="上传固件：" prop="logo">
        <ApkSingleUpload v-model="deviceUpgrade.apkUrl" :acceptType="['file']"
                         :lastVersionInt="lastAppUpgrade==null?null:lastAppUpgrade.versionCode"
                         :lastVersion="lastAppUpgrade==null?null:lastAppUpgrade.versionName"
                         @info="onApkInfoInput"
                         @input="onFileInput" style="width: 300px"></ApkSingleUpload>
      </el-form-item>

      <!--      <el-form-item label="品牌专区大图：">-->
      <!--        <single-upload v-model="brand.bigPic"></single-upload>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="品牌故事：">-->
      <!--        <el-input-->
      <!--          placeholder="请输入内容"-->
      <!--          type="textarea"-->
      <!--          v-model="brand.brandStory"-->
      <!--          :autosize="true"></el-input>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="排序：" prop="sort">-->
      <!--        <el-input v-model.number="brand.sort"></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="强制更新：">
        <el-radio-group v-model="deviceUpgrade.pressing">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="是否显示：">
        <el-radio-group v-model="deviceUpgrade.showStatus">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <!--      <el-form-item label="品牌制造商：">-->
      <!--        <el-radio-group v-model="brand.factoryStatus">-->
      <!--          <el-radio :label="1">是</el-radio>-->
      <!--          <el-radio :label="0">否</el-radio>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" @click="onSubmit('brandFrom')">提交</el-button>
        <el-button v-if="!isEdit" @click="resetForm('brandFrom')">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>
<script>
import {
  createAppUpgrade,
  getAppUpgrade,
  updateAppUpgrade,
  getLastAppUpgrade
} from '@/api/appUpgrade'

import ApkSingleUpload from '@/components/Upload/ApkSingleUpload'

const defaultDeviceUpgrade = {
  versionName: null,
  versionCode: null,
  apkUrl: null,
  description: null,
  pressing: null,
};
export default {
  name: 'AppUpgradeDetail',
  components: {ApkSingleUpload},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      deviceUpgrade: Object.assign({}, defaultDeviceUpgrade),
      pixelPicSizes: [],//尺寸大小
      lastAppUpgrade: null,
      rules: {
        description: [
          {required: true, message: '请输入更新说明', trigger: 'blur'},
          {min: 2, max: 140, message: '长度在 2 到 140 个字符', trigger: 'blur'}
        ],
        apkUrl: [
          {required: true, message: '请上传APK', trigger: 'blur'}
        ],
        deviceSize: [
          {required: true, message: '请选择设备尺寸', trigger: 'blur'}
        ],
      }
    }
  },
  created() {
    if (this.isEdit) {
      getAppUpgrade(this.$route.query.id).then(response => {
        this.deviceUpgrade = response.data;
      });
    } else {
      this.deviceUpgrade = Object.assign({}, defaultDeviceUpgrade);
      this.loadAppUpgrade();
    }
  },
  watch: {},
  methods: {

    loadAppUpgrade() {
      getLastAppUpgrade().then(response => {
        this.lastAppUpgrade = response.data;
      });
    },
    onFileInput(e) {
      console.log("onFileInput " + e)
      // let file = new File(null, e);
      // if (!file) {
      //   return;
      // }
      // let reader = new FileReader();
      // reader.onload = function (e) {
      //   let img = new Image();
      //   img.onload = function () {
      //     console.log(`Image size: ${img.width}x${img.height}`);
      //   };
      // };
    },
    onApkInfoInput(e) {
      console.log("onApkInfoInput " + JSON.stringify(e))
      // let file = new File(null, e);
      // if (!file) {
      //   return;
      // }
      // let reader = new FileReader();
      // reader.onload = function (e) {
      //   let img = new Image();
      //   img.onload = function () {
      //     console.log(`Image size: ${img.width}x${img.height}`);
      //   };
      // };
      if (e) {
        this.deviceUpgrade.versionCode = e.versionCode;
        this.deviceUpgrade.versionName = e.versionName;
      } else {
        this.deviceUpgrade.versionCode = null;
        this.deviceUpgrade.versionName = null;
      }
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        // console.log("版本比较 " + (this.deviceUpgrade.version.localeCompare(this.lastDeviceUpgrade.version))) //验证成功
        // if (this.lastDeviceUpgrade != null && this.deviceUpgrade.version.localeCompare(this.lastDeviceUpgrade.version) <= 0) {
        //   this.$message({
        //     message: '新版本需大于最新版本',
        //     type: 'error',
        //     duration: 1000
        //   });
        //   return;
        // }
        if (valid) {
          this.$confirm('是否提交数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            if (this.isEdit) {
              updateAppUpgrade(this.$route.query.id, this.deviceUpgrade).then(response => {
                this.$refs[formName].resetFields();
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  duration: 1000
                });
                this.$router.back();
              });
            } else {
              createAppUpgrade(this.deviceUpgrade).then(response => {
                this.$refs[formName].resetFields();
                this.pixelPic = Object.assign({}, defaultDeviceUpgrade);
                this.$message({
                  message: '提交成功',
                  type: 'success',
                  duration: 1000
                });
                this.$router.back();
              });
            }
          });

        } else {
          this.$message({
            message: '验证失败',
            type: 'error',
            duration: 1000
          });
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.pixelPic = Object.assign({}, defaultPixelPic);
    }
  }
}
</script>
<style>
</style>


