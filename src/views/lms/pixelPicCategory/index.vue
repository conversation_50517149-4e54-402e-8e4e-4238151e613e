<template>
  <div class="app-container">
    <el-card class="operate-container" shadow="never">
      <i class="el-icon-tickets" style="margin-top: 5px"></i>
      <span style="margin-top: 5px">数据列表</span>
      <el-button
        class="btn-add"
        @click="onCreateClick()"
        size="mini">
        添加
      </el-button>
    </el-card>
    <div class="table-container">
      <el-table ref="productAttrCateTable"
                style="width: 100%"
                :data="list"
                v-loading="listLoading"
                border>
        <el-table-column label="编号" width="100" align="center">
          <template slot-scope="scope">{{ scope.row.id }}</template>
        </el-table-column>
        <el-table-column label="图标" align="center" width="100">
          <template slot-scope="scope">
            <img :src="scope.row.icon" height="40"
                 alt="" v-if="scope.row.icon!==null&& scope.row.icon!==''"/> <label v-else>{{ '未设置' }}</label>
          </template>
        </el-table-column>
        <!--        <el-table-column label="测试icon" align="center">-->
        <!--          <template slot-scope="scope"><img :src="scope.row.icon" height="80"-->
        <!--                                            alt=""/></template>-->
        <!--        </el-table-column>-->
        <el-table-column label="类型名称" align="center">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>


        <!--        <el-table-column label="属性数量" width="200" align="center">-->
        <!--          <template slot-scope="scope">{{ scope.row.attributeCount == null ? 0 : scope.row.attributeCount }}</template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column label="参数数量" width="200" align="center">-->
        <!--          <template slot-scope="scope">{{ scope.row.paramCount == null ? 0 : scope.row.paramCount }}</template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column label="设置" width="200" align="center">-->
        <!--          <template slot-scope="scope">-->
        <!--            <el-button-->
        <!--              size="mini"-->
        <!--              @click="getAttrList(scope.$index, scope.row)">属性列表-->
        <!--            </el-button>-->
        <!--            <el-button-->
        <!--              size="mini"-->
        <!--              @click="getParamList(scope.$index, scope.row)">参数列表-->
        <!--            </el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column label="操作" width="200" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              @click="handleUpdate(scope.$index, scope.row)">编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, sizes,prev, pager, next,jumper"
        :page-size="listQuery.pageSize"
        :page-sizes="[5,10,15]"
        :current-page.sync="listQuery.pageNum"
        :total="total">
      </el-pagination>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :before-close="handleClose()"
      width="30%">
      <el-form ref="productAttrCatForm" :model="productAttrCate" :rules="rules" label-width="120px">
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="productAttrCate.name" auto-complete="off" clearable maxlength="20"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleConfirm('productAttrCatForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  fetchList,
  createPixelPicCategory,
  deletePixelPicCategory,
  updatePixelPicCategory
} from '@/api/pixelPicCategory'

export default {
  name: 'productAttrCateList',
  data() {
    return {
      list: null,
      total: null,
      listLoading: true,
      listQuery: {
        pageNum: 1,
        pageSize: 5
      },
      dialogVisible: false,
      dialogTitle: '',
      productAttrCate: {
        name: '',
        id: null
      },
      rules: {
        name: [
          {required: true, message: '请输入类型名称', trigger: 'blur'}
        ]
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.listLoading = true;
      fetchList(this.listQuery).then(response => {
        this.listLoading = false;
        this.list = response.data.list;
        this.total = response.data.total;
      });
    },
    addProductAttrCate() {
      this.dialogVisible = true;
      this.productAttrCate.name = ''
      this.dialogTitle = "添加类型";
    },
    handleSizeChange(val) {
      this.listQuery.pageNum = 1;
      this.listQuery.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.pageNum = val;
      this.getList();
    },
    onCreateClick() {
      this.$router.push({path: '/lms/createPixelPicCategory'})
    },
    handleUpdate(index, row) {
      this.$router.push({path: '/lms/updatePixelPicCategory', query: {id: row.id}})
    },
    handleDelete(index, row) {
      this.$confirm('是否要删除该类型', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePixelPicCategory(row.id).then(response => {
          this.$message({
            message: '删除成功',
            type: 'success',
            duration: 1000
          });
          this.getList();
        });
      });
    },
    getAttrList(index, row) {
      this.$router.push({path: '/pms/productAttrList', query: {cid: row.id, cname: row.name, type: 0}})
    },
    getParamList(index, row) {
      this.$router.push({path: '/pms/productAttrList', query: {cid: row.id, cname: row.name, type: 1}})
    },
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {};
          data['name'] = this.productAttrCate.name;
          if (this.dialogTitle === "添加类型") {
            createPixelPicCategory(data).then(response => {
              this.$message({
                message: '添加成功',
                type: 'success',
                duration: 1000
              });
              this.dialogVisible = false;
              this.getList();
            });
          } else {
            updatePixelPicCategory(this.productAttrCate.id, data).then(response => {
              this.$message({
                message: '修改成功',
                type: 'success',
                duration: 1000
              });
              this.dialogVisible = false;
              this.getList();
            });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleClose() {
      if (!this.dialogVisible && this.$refs.productAttrCatForm) {
        this.$refs.productAttrCatForm.clearValidate()
      }
    }
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
</style>


