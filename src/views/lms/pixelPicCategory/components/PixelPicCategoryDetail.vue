<template>
  <el-card class="form-container" shadow="never">
    <el-form :model="pixelPic" :rules="rules" ref="brandFrom" label-width="150px">
      <el-form-item label="类型名称：" prop="name">
        <el-input v-model="pixelPic.name" style="width: 200px" maxlength="20" clearable></el-input>
      </el-form-item>


      <el-form-item label="上传Icon：" prop="logo">
        <IconSingleUpload v-model="pixelPic.icon" :acceptType="['svg','png']" :acceptSize="pixelPic.imgSize"
                          @input="onFileInput"></IconSingleUpload>
      </el-form-item>
      <el-form-item :label="item.name" prop="name" v-for="item in langs" :key="item.code">
        <el-input v-model="item.value" style="width: 200px" maxlength="20" clearable></el-input>
      </el-form-item>
      <el-form-item label="是否显示：">
        <el-radio-group v-model="pixelPic.showStatus">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <!--      <el-form-item label="品牌制造商：">-->
      <!--        <el-radio-group v-model="brand.factoryStatus">-->
      <!--          <el-radio :label="1">是</el-radio>-->
      <!--          <el-radio :label="0">否</el-radio>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" @click="onSubmit('brandFrom')">提交</el-button>
        <el-button v-if="!isEdit" @click="resetForm('brandFrom')">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>
<script>
import {
  fetchList,
  createPixelPicCategory,
  updatePixelPicCategory,
  getPixelPicCategory
} from '@/api/pixelPicCategory'
import IconSingleUpload from '@/components/Upload/IconSingleUpload'
import {box} from "echarts/lib/util/layout";

const defaultPixelPic = {
  icon: '',
  name: '',
  langs: null,
};
export default {
  name: 'PixelPicCategoryDetail',
  components: {IconSingleUpload},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pixelPic: Object.assign({}, defaultPixelPic),
      pixelPicCategoryList: [],//类型
      pixelPicSizes: [],//尺寸大小
      /**
       *
       */
      langs:
        [
          {
            "code": "zh_Hans",
            "name": "简体中文",
            "value": null,
          },
          {
            "code": "zh_Hant",
            "name": "繁体中文",
            "value": null,
          },
          {
            "code": "en",
            "name": "英语",
            "value": null,
          },
          {
            "code": "de",
            "name": "德语",
            "value": null,
          },
          {
            "code": "fr",
            "name": "法语",
            "value": null,
          },
          {
            "code": "ru",
            "name": "俄语",
            "value": null,
          },
          {
            "code": "th",
            "name": "泰语",
            "value": null,
          },
          {
            "code": "it",
            "name": "意大利语",
            "value": null,
          },
          {
            "code": "pt",
            "name": "葡萄牙语",
            "value": null,
          },
          {
            "code": "es",
            "name": "西班牙语",
            "value": null,
          },
          {
            "code": "vi",
            "name": "越南语",
            "value": null,
          },
          {
            "code": "ko",
            "name": "韩语",
            "value": null,
          },
          {
            "code": "ja",
            "name": "日语",
            "value": null,
          },
          {
            "code": "ar",
            "name": "阿拉伯语",
            "value": null,
          },
          {
            "code": "hi",
            "name": "印度语",
            "value": null,
          }
        ],
      rules: {
        name: [
          {required: true, message: '请输入图片名称', trigger: 'blur'},
          {min: 2, max: 140, message: '长度在 2 到 140 个字符', trigger: 'blur'}
        ],
        icon: [
          {required: true, message: '请上传图片', trigger: 'blur'}
        ],
        langs: [
          {required: true, message: '请输入各个语言配置', trigger: 'blur'}
        ],
      }
    }
  },
  created() {
    if (this.isEdit) {
      getPixelPicCategory(this.$route.query.id).then(response => {
        console.log("getPixelPicCategory " + JSON.stringify(response))
        this.pixelPic = response.data;
        let value;
        let item;
        let responseLangs = response.data.langs;

        const langsMap = JSON.parse(responseLangs);

        for (let i = 0; i < this.langs.length; i++) {
          item = this.langs[i];
          value = langsMap[item.code];
          // console.log(item.code + " value " + value)
          if (value !== null && value !== "") {
            this.langs[i].value = value;
          }
        }
      });
    } else {
      this.pixelPic = Object.assign({}, defaultPixelPic);
    }
  },
  methods: {
    onFileInput(e) {
      console.log("onFileInput " + e)
      // let file = new File(null, e);
      // if (!file) {
      //   return;
      // }
      // let reader = new FileReader();
      // reader.onload = function (e) {
      //   let img = new Image();
      //   img.onload = function () {
      //     console.log(`Image size: ${img.width}x${img.height}`);
      //   };
      // };
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let langs = this.langs;
          let checkLangsResult = true;
          for (let i = 0; i < langs.length; i++) {
            if (langs[i].value == null || langs[i].value === '') {
              checkLangsResult = false;
              break;
            }
          }
          console.log("checkLangsResult " + checkLangsResult)
          if (!checkLangsResult) {
            this.$message({
              message: '有语言未配置',
              type: 'error',
              duration: 1000
            });
            return;
          }
          let langMap = {};
          for (let i = 0; i < langs.length; i++) {
            if (langs[i].value === null || langs[i].value === '') {
            } else {
              langMap[langs[i].code] = langs[i].value;
            }
          }
          this.pixelPic.langs = langMap;

          this.$confirm('是否提交数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            if (this.isEdit) {
              updatePixelPicCategory(this.$route.query.id, this.pixelPic).then(response => {
                this.$refs[formName].resetFields();
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  duration: 1000
                });
                this.$router.back();
              });
            } else {
              createPixelPicCategory(this.pixelPic).then(response => {
                this.$refs[formName].resetFields();
                this.pixelPic = Object.assign({}, defaultPixelPic);
                this.$message({
                  message: '提交成功',
                  type: 'success',
                  duration: 1000
                });
                this.$router.back();
              });
            }
          });

        } else {
          this.$message({
            message: '验证失败',
            type: 'error',
            duration: 1000
          });
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.pixelPic = Object.assign({}, defaultPixelPic);
    }
  }
}
</script>
<style>
</style>


