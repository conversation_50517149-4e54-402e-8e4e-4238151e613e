<template>
  <el-card class="form-container" shadow="never" disabled>
    <el-form :model="deviceUpgrade" :rules="rules" ref="brandFrom" label-width="150px">
      <el-form-item label="更新说明：" prop="name">
        <el-input v-model="deviceUpgrade.description" style="width: 200px" maxlength="20" clearable></el-input>
      </el-form-item>
      <!--      <el-form-item label="图片类型：">-->
      <!--        &lt;!&ndash;        <el-input v-model="pixelPic.imgType"></el-input>&ndash;&gt;-->
      <!--        <el-select v-model="pixelPic.imgType" placeholder="请选择">-->
      <!--          <el-option-->
      <!--            v-for="item in pixelPicCategoryList"-->
      <!--            :key="item.id"-->
      <!--            :label="item.name"-->
      <!--            :value="item.id">-->
      <!--          </el-option>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item label="设备尺寸：">
        <!--        <el-input v-model="pixelPic.imgType"></el-input>-->
        <el-select v-model="deviceUpgrade.deviceSize" placeholder="请选择">
          <el-option
            v-for="item in pixelPicSizes"
            :key="item.text"
            :label="item.text"
            :value="item.text">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <template> {{
            this.lastDeviceUpgrade == null ? '没最新版本' : '最新版本:' + this.lastDeviceUpgrade.version
          }}
        </template>
      </el-form-item>
      <el-form-item label="新版本号：" prop="name">
        <el-input v-model="deviceUpgrade.version" style="width: 200px" maxlength="10" clearable></el-input>
      </el-form-item>
      <el-form-item label="上传固件：" prop="logo">
        <UpgradeSingleUpload v-model="deviceUpgrade.binUrl" :acceptType="['file']"
                             @input="onFileInput" style="width: 300px"></UpgradeSingleUpload>
      </el-form-item>

      <!--      <el-form-item label="品牌专区大图：">-->
      <!--        <single-upload v-model="brand.bigPic"></single-upload>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="品牌故事：">-->
      <!--        <el-input-->
      <!--          placeholder="请输入内容"-->
      <!--          type="textarea"-->
      <!--          v-model="brand.brandStory"-->
      <!--          :autosize="true"></el-input>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="排序：" prop="sort">-->
      <!--        <el-input v-model.number="brand.sort"></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="是否显示：">
        <el-radio-group v-model="deviceUpgrade.showStatus">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <!--      <el-form-item label="品牌制造商：">-->
      <!--        <el-radio-group v-model="brand.factoryStatus">-->
      <!--          <el-radio :label="1">是</el-radio>-->
      <!--          <el-radio :label="0">否</el-radio>-->
      <!--        </el-radio-group>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" @click="onSubmit('brandFrom')">提交</el-button>
        <el-button v-if="!isEdit" @click="resetForm('brandFrom')">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>
<script>
import {fetchSizeList} from '@/api/pixelPic'
import {
  createDeviceUpgrade,
  getDeviceUpgrade,
  updateDeviceUpgrade,
  getLastDeviceUpgradeByDeviceSize
} from '@/api/deviceUpgrade'

import UpgradeSingleUpload from '@/components/Upload/UpgradeSingleUpload'

const VERSION_REG = "^[0-9.]+$";

const defaultDeviceUpgrade = {
  deviceSize: '32x16',
  version: null,
  versionInt: null,
  binUrl: null,
  description: null,
};
export default {
  name: 'DeviceUpgradeDetail',
  components: {UpgradeSingleUpload},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      deviceUpgrade: Object.assign({}, defaultDeviceUpgrade),
      pixelPicSizes: [],//尺寸大小
      lastDeviceUpgrade: null,
      rules: {
        description: [
          {required: true, message: '请输入更新说明', trigger: 'blur'},
          {min: 2, max: 140, message: '长度在 2 到 140 个字符', trigger: 'blur'}
        ],
        binUrl: [
          {required: true, message: '请上传固件', trigger: 'blur'}
        ],
        deviceSize: [
          {required: true, message: '请选择设备尺寸', trigger: 'blur'}
        ],
        version: [
          {required: true, message: '请选择版本号', trigger: 'blur'}
        ],
      }
    }
  },
  created() {
    if (this.isEdit) {
      getDeviceUpgrade(this.$route.query.id).then(response => {
        this.deviceUpgrade = response.data;
      });
    } else {
      this.deviceUpgrade = Object.assign({}, defaultDeviceUpgrade);
      this.loadDeviceUpgradeByDeviceSize();
    }
    this.loadPixelPicSize();
  },
  watch: {
    'deviceUpgrade.deviceSize': function (newValue) {
      this.loadDeviceUpgradeByDeviceSize();
    }
  },
  methods: {
    loadPixelPicSize() {
      fetchSizeList().then(response => {
        this.pixelPicSizes = response.data;
        if (this.pixelPicSizes.length > 0) {
          this.deviceUpgrade.deviceSize = this.pixelPicSizes[0].text;
        }
      });
    },
    loadDeviceUpgradeByDeviceSize() {
      getLastDeviceUpgradeByDeviceSize(this.deviceUpgrade.deviceSize).then(response => {
        this.lastDeviceUpgrade = response.data;
        if (this.lastDeviceUpgrade == null) {
          this.deviceUpgrade.version = '1.0.0';
          this.deviceUpgrade.versionInt = 1;
        } else {
          // 拆分版本号
          let versionParts = this.lastDeviceUpgrade.version.split('.').map(Number);
          console.log("versionParts[2].type " + typeof versionParts[2])

          // 递增修订号
          versionParts[2] += 1;

          console.log("versionParts[2].value " + versionParts[2])

          // 处理进位
          if (versionParts[2] > 255) {
            versionParts[2] = 0;
            versionParts[1] += 1;
            if (versionParts[1] > 255) {
              versionParts[1] = 0;
              versionParts[0] += 1;
            }
          }

          // 重新组合版本号
          this.deviceUpgrade.version = versionParts.join('.');

          // this.deviceUpgrade.version = this.lastDeviceUpgrade.version + 1;
          this.deviceUpgrade.versionInt = this.lastDeviceUpgrade.versionInt + 1;
        }
      });
    },
    onFileInput(e) {
      console.log("onFileInput " + e)
      // let file = new File(null, e);
      // if (!file) {
      //   return;
      // }
      // let reader = new FileReader();
      // reader.onload = function (e) {
      //   let img = new Image();
      //   img.onload = function () {
      //     console.log(`Image size: ${img.width}x${img.height}`);
      //   };
      // };
    },
    onSubmit(formName) {


      this.$refs[formName].validate((valid) => {
        // console.log("版本比较 " + (this.deviceUpgrade.version.localeCompare(this.lastDeviceUpgrade.version))) //验证成功
        // if (this.lastDeviceUpgrade != null && this.deviceUpgrade.version.localeCompare(this.lastDeviceUpgrade.version) <= 0) {
        //   this.$message({
        //     message: '新版本需大于最新版本',
        //     type: 'error',
        //     duration: 1000
        //   });
        //   return;
        // }
        if (!this.deviceUpgrade.version.match(VERSION_REG)) {
          this.$message({
            message: '版本号只允许输入数字和.',
            type: 'error',
            duration: 1000
          });
          return;
        }
        if (valid) {
          this.$confirm('是否提交数据', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            if (this.isEdit) {
              updateDeviceUpgrade(this.$route.query.id, this.deviceUpgrade).then(response => {
                this.$refs[formName].resetFields();
                this.$message({
                  message: '修改成功',
                  type: 'success',
                  duration: 1000
                });
                this.$router.back();
              });
            } else {
              createDeviceUpgrade(this.deviceUpgrade).then(response => {
                this.$refs[formName].resetFields();
                this.pixelPic = Object.assign({}, defaultDeviceUpgrade);
                this.$message({
                  message: '提交成功',
                  type: 'success',
                  duration: 1000
                });
                this.$router.back();
              });
            }
          });

        } else {
          this.$message({
            message: '验证失败',
            type: 'error',
            duration: 1000
          });
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.pixelPic = Object.assign({}, defaultPixelPic);
    }
  }
}
</script>
<style>
</style>


