import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '../views/layout/Layout'

/**
 * hidden: true                   if `hidden:true` will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu, whatever its child routes length
 *                                if not set alwaysShow, only more than one route under the children
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noredirect           if `redirect:noredirect` will no redirct in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
 title: 'title'               the name show in submenu and breadcrumb (recommend set)
 icon: 'svg-name'             the icon show in the sidebar,
 }
 **/
export const constantRouterMap = [
  {path: '/login', component: () => import('@/views/login/index'), hidden: true},
  {path: '/404', component: () => import('@/views/404'), hidden: true},
  {
    path: '',
    component: Layout,
    redirect: '/lms',
    meta: {title: '首页', icon: 'home'},
    children: [
      // {
      //   path: 'home',
      //   name: 'home',
      //   component: () => import('@/views/home/<USER>'),
      //   meta: {title: '仪表盘', icon: 'dashboard'}
      // },
      // {
      //   name: 'document',
      //   path: 'https://www.macrozheng.com',
      //   meta: {title: '学习教程', icon: 'document'}
      // },
      // {
      //   name: 'video',
      //   path: 'https://www.macrozheng.com/mall/catalog/mall_video.html',
      //   meta: {title: '视频教程', icon: 'video'}
      // },
    ]
  }
]

export const asyncRouterMap = [
  {
    path: '/lms',
    component: Layout,
    redirect: '/lms/pixelPic',
    name: 'lms',
    meta: {title: '图片操作', icon: 'product'},
    children: [
      {
        path: 'pixelPic',
        name: 'pixelPic',
        component: () => import('@/views/lms/pixelPic/index'),
        meta: {title: '灯列表', icon: 'product-list'}
      },
      {
        path: 'addPixelPic',
        name: 'addPixelPic',
        component: () => import('@/views/lms/pixelPic/add'),
        meta: {title: '添加灯', icon: 'product-add'}
      },
      {
        path: 'updatePixelPic',
        name: 'updatePixelPic',
        component: () => import('@/views/lms/pixelPic/update'),
        meta: {title: '修改灯', icon: 'product-add'},
        hidden: true
      },
      {
        path: 'pixelPicCategory',
        name: 'pixelPicCategory',
        component: () => import('@/views/lms/pixelPicCategory/index'),
        meta: {title: '图片类型', icon: 'product-add'},
        hidden: true
      },
      {
        path: 'createPixelPicCategory',
        name: 'createPixelPicCategory',
        component: () => import('@/views/lms/pixelPicCategory/add'),
        meta: {title: '新增类型', icon: 'product-add'},
        hidden: true
      },
      {
        path: 'updatePixelPicCategory',
        name: 'updatePixelPicCategory',
        component: () => import('@/views/lms/pixelPicCategory/update'),
        meta: {title: '修改类型', icon: 'product-add'},
        hidden: true
      },
      {
        path: 'upgrade',
        name: 'upgrade',
        component: () => import('@/views/lms/upgrade/index'),
        meta: {title: '固件升级', icon: 'product-list'}
      },
      {
        path: 'addUpgrade',
        name: 'addUpgrade',
        component: () => import('@/views/lms/upgrade/add'),
        meta: {title: '添加固件', icon: 'product-add'}
      },
      {
        path: 'updateUpgrade',
        name: 'updateUpgrade',
        component: () => import('@/views/lms/upgrade/update'),
        meta: {title: '修改固件', icon: 'product-add'},
        hidden: true
      },
      {
        path: 'appUpgrade',
        name: 'appUpgrade',
        component: () => import('@/views/lms/appUpgrade/index'),
        meta: {title: 'APP升级', icon: 'product-list'}
      },
      {
        path: 'addAppUpgrade',
        name: 'addAppUpgrade',
        component: () => import('@/views/lms/appUpgrade/add'),
        meta: {title: '添加APP版本', icon: 'product-add'}
      },
      {
        path: 'updateAppUpgrade',
        name: 'updateAppUpgrade',
        component: () => import('@/views/lms/appUpgrade/update'),
        meta: {title: '修改APP版本', icon: 'product-add'},
        hidden: true
      },
    ]
  },
  {
    path: '/ums',
    component: Layout,
    redirect: '/ums/admin',
    name: 'ums',
    meta: {title: '权限', icon: 'ums'},
    children: [
      {
        path: 'admin',
        name: 'admin',
        component: () => import('@/views/ums/admin/index'),
        meta: {title: '用户列表', icon: 'ums-admin'}
      },
      {
        path: 'role',
        name: 'role',
        component: () => import('@/views/ums/role/index'),
        meta: {title: '角色列表', icon: 'ums-role'}
      },
      {
        path: 'allocMenu',
        name: 'allocMenu',
        component: () => import('@/views/ums/role/allocMenu'),
        meta: {title: '分配菜单'},
        hidden: true
      },
      {
        path: 'allocResource',
        name: 'allocResource',
        component: () => import('@/views/ums/role/allocResource'),
        meta: {title: '分配资源'},
        hidden: true
      },
      {
        path: 'menu',
        name: 'menu',
        component: () => import('@/views/ums/menu/index'),
        meta: {title: '菜单列表', icon: 'ums-menu'}
      },
      {
        path: 'addMenu',
        name: 'addMenu',
        component: () => import('@/views/ums/menu/add'),
        meta: {title: '添加菜单'},
        hidden: true
      },
      {
        path: 'updateMenu',
        name: 'updateMenu',
        component: () => import('@/views/ums/menu/update'),
        meta: {title: '修改菜单'},
        hidden: true
      },
      {
        path: 'resource',
        name: 'resource',
        component: () => import('@/views/ums/resource/index'),
        meta: {title: '资源列表', icon: 'ums-resource'}
      },
      {
        path: 'resourceCategory',
        name: 'resourceCategory',
        component: () => import('@/views/ums/resource/categoryList'),
        meta: {title: '资源分类'},
        hidden: true
      }
    ]
  },
  {path: '*', redirect: '/404', hidden: true}
]

export default new Router({
  // mode: 'history', //后端支持可开
  scrollBehavior: () => ({y: 0}),
  routes: constantRouterMap
})

