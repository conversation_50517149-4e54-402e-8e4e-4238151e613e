# 跨域问题解决方案

## 问题描述
调用 `https://app.popusign.com.cn:8201/admin/ums/login` 不会出现跨域错误，但调用 `https://app.popusign.com.cn:8201/admin/ums/info` 时出现跨域错误。

## 问题原因
1. **配置冲突**：同时存在 `vue.config.js` 和 `config/index.js` 中的代理配置
2. **BASE_API 配置错误**：直接指向远程服务器而不是本地代理
3. **代理配置不正确**：target 路径配置错误

## 解决方案

### 1. 修复 vue.config.js
```javascript
module.exports = {
    devServer: {
      proxy: {
        '/admin': {
          target: 'https://app.popusign.com.cn:8201', // 修正：移除路径部分
          changeOrigin: true,
          secure: false, // 新增：处理 HTTPS
          pathRewrite: {
            // 保持 /admin 前缀，不需要重写
          },
          headers: {
            'Connection': 'keep-alive' // 修正：拼写错误
          },
          logLevel: 'debug' // 新增：调试日志
        }
      }
    }
}
```

### 2. 修复 config/dev.env.js
```javascript
module.exports = merge(prodEnv, {
  NODE_ENV: '"development"',
  BASE_API: '""' // 修改：使用空字符串，通过代理转发
})
```

### 3. 更新 config/index.js
在 `proxyTable` 中添加代理配置作为备用。

## 验证步骤
1. 重启开发服务器：`npm run dev`
2. 检查浏览器网络面板，确认请求是通过本地代理发送
3. 登录后检查获取用户信息接口是否正常

## 注意事项
- 确保所有 API 请求都使用相对路径 `/admin/...`
- 代理只在开发环境生效，生产环境需要服务器配置 CORS
- 如果仍有问题，检查浏览器控制台的详细错误信息
